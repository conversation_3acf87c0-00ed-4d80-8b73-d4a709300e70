# -*- coding: utf-8 -*-
"""
简化版模型配置管理
专注于核心AI API调用功能，移除复杂的负载均衡和备用端点
"""

from openai import OpenAI
import json
import os
import time
import re
import math
from datetime import datetime
from typing import Dict, Optional, Any, Tuple, List

# 尝试导入 tiktoken 进行精确 token 计算
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    TIKTOKEN_ENCODING = tiktoken.get_encoding("cl100k_base")
    print("✅ 已启用tiktoken精确token计算")
except ImportError:
    TIKTOKEN_AVAILABLE = False
    TIKTOKEN_ENCODING = None
    print("⚠️ tiktoken未安装，使用近似token计算")


class IntelligentErrorHandler:
    """智能错误处理器 - 处理AI API的特定错误模式"""

    @staticmethod
    def estimate_tokens(text: str) -> int:
        """估算文本的token数量"""
        if TIKTOKEN_AVAILABLE and TIKTOKEN_ENCODING:
            try:
                return len(TIKTOKEN_ENCODING.encode(text))
            except Exception as e:
                print(f"⚠️ tiktoken计算失败: {e}")

        # Fallback到近似估算
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars

        # 中文字符：每个字符约1个token，英文字符：每3-4个字符约1个token
        estimated_tokens = chinese_chars + (other_chars // 3)
        return max(1, estimated_tokens)

    @staticmethod
    def estimate_messages_tokens(messages: List[Dict]) -> int:
        """估算消息列表的总token数"""
        total_tokens = 0

        for message in messages:
            # 消息结构的开销（role, content等字段）
            total_tokens += 4

            # 内容token数
            content = message.get('content', '')
            if content:
                total_tokens += IntelligentErrorHandler.estimate_tokens(content)

        # 对话结构的额外开销
        total_tokens += 2

        return total_tokens

    @staticmethod
    def parse_token_limit_error(error_str: str) -> Tuple[bool, Optional[int], Optional[int]]:
        """解析token超限错误，提取当前token数和最大限制

        Args:
            error_str: 错误消息字符串

        Returns:
            Tuple[是否token错误, 当前token数, 最大token数]
        """
        error_lower = error_str.lower()

        # 检查是否为token超限错误的关键词
        token_limit_keywords = [
            "token count", "token limit", "exceeds the maximum",
            "too many tokens", "context length", "maximum number of tokens",
            "input token count", "input too long", "current tokens",
            "invalid_argument"
        ]

        is_token_error = any(keyword in error_lower for keyword in token_limit_keywords)

        if not is_token_error:
            return False, None, None

        # 尝试提取具体的token数量 - 支持多种错误格式
        patterns = [
            # Google Gemini API格式: "The input token count (1697036) exceeds the maximum number of tokens allowed (1048576)."
            r'input token count \((\d+)\).*?maximum.*?tokens.*?allowed \((\d+)\)',
            # 标准格式
            r'input token count \((\d+)\).*?maximum.*?\((\d+)\)',
            # "Input too long: 1093777 tokens, maximum 1048576"
            r'input too long.*?(\d+) tokens.*?maximum (\d+)',
            # OpenRouter格式: "you requested about 1095078 tokens... maximum context length is 1048576 tokens"
            r'you requested about (\d+) tokens.*?maximum context length is (\d+) tokens',
            # 通用格式
            r'token count.*?(\d+).*?limit.*?(\d+)',
            # "Current tokens: 950000, max allowed: 1048576"
            r'current tokens.*?(\d+).*?max.*?(\d+)',
            # exceeds格式
            r'(\d+).*?exceeds.*?(\d+)',
            # "1093777 tokens, maximum 1048576"
            r'(\d+) tokens.*?maximum (\d+)',
            # "tokens: XXX, max: YYY"
            r'tokens.*?(\d+).*?max.*?(\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, error_lower)
            if match:
                current_tokens = int(match.group(1))
                max_tokens = int(match.group(2))
                print(f"🎯 从API错误中提取token信息: 当前{current_tokens:,}, 最大{max_tokens:,}")
                return True, current_tokens, max_tokens

        # 如果无法提取具体数字，至少确认是token错误
        print(f"🔍 检测到token超限错误，但无法提取具体数字")
        return True, None, None

    @staticmethod
    def parse_rate_limit_error(error_str: str) -> Tuple[bool, Optional[int]]:
        """解析速率限制错误，提取重试延迟时间

        Args:
            error_str: 错误消息字符串

        Returns:
            Tuple[是否速率限制错误, 建议重试延迟秒数]
        """
        error_lower = error_str.lower()

        # 检查是否为速率限制错误
        rate_limit_keywords = [
            "429", "rate limit", "too many requests", "quota exceeded",
            "requests per minute", "requests per day", "resource_exhausted",
            "exceeded your current quota", "generatecontentpaidtierinputtokenspermodelperminute"
        ]

        is_rate_limit_error = any(keyword in error_lower for keyword in rate_limit_keywords)

        if not is_rate_limit_error:
            return False, None

        # 优先尝试从Google Gemini API的JSON格式中提取retryDelay
        import json
        try:
            # 尝试解析JSON格式的错误消息
            if "error code:" in error_lower and "[{" in error_str:
                # 提取JSON部分: Error code: 429 - [{'error': {...}}]
                json_start = error_str.find("[")
                json_end = error_str.rfind("]") + 1
                if json_start != -1 and json_end > json_start:
                    json_str = error_str[json_start:json_end]
                    error_data = json.loads(json_str)

                    # 查找retryDelay字段
                    if isinstance(error_data, list) and len(error_data) > 0:
                        error_obj = error_data[0]
                        if isinstance(error_obj, dict) and 'error' in error_obj:
                            error_details = error_obj['error']
                            if isinstance(error_details, dict) and 'retryDelay' in error_details:
                                retry_delay_str = error_details['retryDelay']
                                # retryDelay格式通常是 "31s" 或 "31.5s"
                                if isinstance(retry_delay_str, str) and retry_delay_str.endswith('s'):
                                    delay_seconds = float(retry_delay_str[:-1])
                                    delay = int(delay_seconds)
                                    print(f"🎯 从Google Gemini API提取重试延迟: {delay}秒 (retryDelay: {retry_delay_str})")
                                    return True, delay
        except (json.JSONDecodeError, KeyError, ValueError, TypeError) as e:
            # JSON解析失败，继续使用正则表达式
            pass

        # 尝试从错误消息中提取重试延迟 (传统方法)
        retry_delay_patterns = [
            r'retry.*?after.*?(\d+).*?second',
            r'wait.*?(\d+).*?second',
            r'retry.*?in.*?(\d+).*?second',
            r'retrydelay.*?(\d+)',
            r'"retrydelay":\s*"(\d+)s"',  # JSON格式的retryDelay
            r'retrydelay.*?(\d+\.?\d*)s',  # 支持小数秒
        ]

        for pattern in retry_delay_patterns:
            match = re.search(pattern, error_lower)
            if match:
                delay = int(float(match.group(1)))  # 支持小数转整数
                print(f"🎯 从API错误中提取重试延迟: {delay}秒")
                return True, delay

        print(f"🔍 检测到速率限制错误，使用默认重试策略")
        return True, None

    @staticmethod
    def reduce_content_to_target_tokens(messages: List[Dict], target_tokens: int) -> List[Dict]:
        """将消息内容减少到目标token数

        Args:
            messages: 原始消息列表
            target_tokens: 目标token数

        Returns:
            减少后的消息列表
        """
        if not messages:
            return messages

        # 计算当前总token数
        current_tokens = IntelligentErrorHandler.estimate_messages_tokens(messages)

        if current_tokens <= target_tokens:
            return messages

        # 计算需要的缩减比例
        reduction_ratio = target_tokens / current_tokens
        print(f"🔄 内容缩减: {current_tokens:,} → {target_tokens:,} tokens (比例: {reduction_ratio:.1%})")

        # 复制消息列表
        reduced_messages = []

        for message in messages:
            reduced_message = message.copy()
            content = message.get('content', '')

            if content and message.get('role') == 'user':
                # 只缩减用户消息的内容，保持系统消息不变
                target_content_length = int(len(content) * reduction_ratio)
                if target_content_length < len(content):
                    # 智能截断：尽量保持完整的句子
                    reduced_content = content[:target_content_length]

                    # 尝试在句号、换行符或逗号处截断
                    for delimiter in ['。', '\n', '，', '.', ',']:
                        last_delimiter = reduced_content.rfind(delimiter)
                        if last_delimiter > target_content_length * 0.8:  # 至少保留80%的内容
                            reduced_content = reduced_content[:last_delimiter + 1]
                            break

                    reduced_message['content'] = reduced_content
                    print(f"📝 用户消息内容缩减: {len(content)} → {len(reduced_content)} 字符")

            reduced_messages.append(reduced_message)

        return reduced_messages

    @staticmethod
    def calculate_exponential_backoff(attempt: int, base_delay: int = 2, max_delay: int = 300) -> int:
        """计算指数退避延迟时间

        Args:
            attempt: 当前尝试次数 (从0开始)
            base_delay: 基础延迟时间(秒)
            max_delay: 最大延迟时间(秒)

        Returns:
            延迟时间(秒)
        """
        delay = base_delay * (2 ** attempt)
        return min(delay, max_delay)


class ModelConfig:
    """简化版模型配置管理类"""

    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.clients = {}  # 缓存客户端实例
        self.error_handler = IntelligentErrorHandler()  # 智能错误处理器
        
    def _load_config(self) -> Dict[str, Any]:
        """加载模型配置"""
        default_config = {
            "providers": {
                "gemini": {
                    "api_key": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
                    "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"
                }
            },
            "models": {
                "step1_analysis": {
                    "provider": "gemini",
                    "model": "gemini-2.5-flash-preview-05-20",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95,
                    "reasoning_effort": "medium"
                },
                "step2_html_generation": {
                    "provider": "gemini",
                    "model": "gemini-2.5-pro-preview-06-05",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_configs(default_config, config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self._save_config(default_config)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            return default_config
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_client(self, provider: str) -> OpenAI:
        """获取指定提供商的OpenAI客户端"""
        if provider not in self.clients:
            provider_config = self.config["providers"][provider]
            self.clients[provider] = OpenAI(
                api_key=provider_config["api_key"],
                base_url=provider_config["base_url"],
                timeout=120.0  # 设置2分钟超时
            )
        
        return self.clients[provider]
    
    def get_model_params(self, step: str) -> Dict[str, Any]:
        """获取指定步骤的模型参数"""
        if step not in self.config["models"]:
            raise ValueError(f"未找到步骤 '{step}' 的配置")
        
        return self.config["models"][step].copy()
    
    def call_api(self, step: str, messages: list, max_retries: int = 3) -> Optional[str]:
        """统一的API调用接口 - 集成智能错误处理"""
        params = self.get_model_params(step)
        provider = params.get("provider", "gemini")
        client = self.get_client(provider)

        # 从参数中提取OpenAI API需要的参数
        api_params = {
            "model": params["model"],
            "messages": messages,
            "temperature": params.get("temperature", 0.3),
            "max_tokens": params.get("max_tokens", 8000),
            "top_p": params.get("top_p", 0.95)
        }

        # Gemini支持reasoning_effort参数
        if "reasoning_effort" in params and provider == "gemini":
            api_params["reasoning_effort"] = params["reasoning_effort"]

        # 智能错误处理的状态跟踪
        current_messages = messages.copy()
        rate_limit_retries = 0
        max_rate_limit_retries = 5
        token_reduction_applied = False  # 跟踪是否应用了token缩减

        for attempt in range(max_retries):
            try:
                model = params["model"]

                # 调试：验证当前使用的消息token数
                current_tokens = self.error_handler.estimate_messages_tokens(current_messages)
                print(f"🤖 调用 {model} API (步骤:{step}, 尝试 {attempt + 1}/{max_retries})...")
                print(f"📊 当前消息Token数: {current_tokens:,} {'(已缩减)' if token_reduction_applied else '(原始)'}")

                # 更新API参数中的消息
                api_params["messages"] = current_messages

                # 执行API调用
                response_content = self._execute_api_call(api_params, client)

                if response_content:
                    print(f"✅ API调用成功，响应长度: {len(response_content)} 字符")
                    return response_content.strip()
                else:
                    print(f"❌ API返回空响应")

            except Exception as e:
                error_str = str(e)

                # 记录错误处理操作的时间戳和线程信息
                error_timestamp = datetime.now().isoformat()
                import threading
                thread_id = threading.get_ident()

                # 统一的错误日志格式，包含线程信息以便调试并发问题
                print(f"❌ [线程{thread_id}] API调用失败 (尝试 {attempt + 1}/{max_retries}): {error_str[:200]}{'...' if len(error_str) > 200 else ''}")

                # 1. 智能处理Token超限错误 (400 - INVALID_ARGUMENT)
                is_token_error, current_tokens, max_tokens = self.error_handler.parse_token_limit_error(error_str)

                if is_token_error:
                    print(f"🎯 [{error_timestamp}] 检测到Token超限错误")

                    if current_tokens and max_tokens:
                        # 使用API提供的精确token信息进行智能缩减
                        target_tokens = int(max_tokens * 0.9)  # 缩减到最大值的90%

                        # 记录缩减前的token数用于验证
                        before_reduction_tokens = self.error_handler.estimate_messages_tokens(current_messages)
                        print(f"📊 智能Token缩减: {current_tokens:,} → {target_tokens:,} (90%)")
                        print(f"🔍 缩减前实际Token数: {before_reduction_tokens:,}")

                        # 自动缩减内容
                        current_messages = self.error_handler.reduce_content_to_target_tokens(
                            current_messages, target_tokens
                        )

                        # 验证缩减后的token数
                        after_reduction_tokens = self.error_handler.estimate_messages_tokens(current_messages)
                        token_reduction_applied = True

                        print(f"✅ 内容已自动缩减: {before_reduction_tokens:,} → {after_reduction_tokens:,} tokens")
                        print(f"🔄 重新尝试API调用 (使用缩减后的消息)...")
                        continue  # 立即重试，不计入attempt次数
                    else:
                        # 无法获取精确token信息，使用估算方式缩减
                        estimated_tokens = self.error_handler.estimate_messages_tokens(current_messages)
                        target_tokens = int(estimated_tokens * 0.9)

                        print(f"📊 估算Token缩减: ~{estimated_tokens:,} → {target_tokens:,} (90%)")

                        current_messages = self.error_handler.reduce_content_to_target_tokens(
                            current_messages, target_tokens
                        )

                        # 验证缩减后的token数
                        after_reduction_tokens = self.error_handler.estimate_messages_tokens(current_messages)
                        token_reduction_applied = True

                        print(f"✅ 内容已估算缩减: {estimated_tokens:,} → {after_reduction_tokens:,} tokens")
                        print(f"🔄 重新尝试API调用 (使用缩减后的消息)...")
                        continue  # 立即重试，不计入attempt次数

                # 2. 智能处理速率限制错误 (429 - RESOURCE_EXHAUSTED)
                is_rate_limit, suggested_delay = self.error_handler.parse_rate_limit_error(error_str)

                if is_rate_limit and rate_limit_retries < max_rate_limit_retries:
                    rate_limit_retries += 1
                    print(f"🎯 [线程{thread_id}] [{error_timestamp}] 检测到速率限制错误 (重试 {rate_limit_retries}/{max_rate_limit_retries})")

                    if suggested_delay:
                        wait_time = min(suggested_delay, 300)  # 最大等待300秒
                        print(f"⏰ [线程{thread_id}] 使用API建议延迟: {wait_time} 秒 (原始: {suggested_delay}秒)")
                    else:
                        # 使用指数退避策略
                        wait_time = self.error_handler.calculate_exponential_backoff(
                            rate_limit_retries - 1, base_delay=2, max_delay=300
                        )
                        print(f"⏰ [线程{thread_id}] 使用指数退避延迟: {wait_time} 秒 (尝试 {rate_limit_retries}/{max_rate_limit_retries})")

                    print(f"💤 [线程{thread_id}] 开始等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                    print(f"🔄 [线程{thread_id}] 等待完成，重新尝试API调用...")
                    continue  # 重试，不计入attempt次数

                # 3. 处理其他类型的错误（保持原有逻辑）
                if attempt < max_retries - 1:
                    if "503" in error_str or "502" in error_str or "server" in error_str.lower():
                        wait_time = min(30, 5 * (2 ** attempt))
                        print(f"⏰ 检测到服务器错误，等待 {wait_time} 秒后重试...")
                    elif "connection" in error_str.lower() or "timeout" in error_str.lower():
                        wait_time = min(15, 3 * (2 ** attempt))
                        print(f"⏰ 检测到连接错误，等待 {wait_time} 秒后重试...")
                    else:
                        wait_time = min(20, 2 ** attempt)
                        print(f"⏰ 其他错误，等待 {wait_time} 秒后重试...")

                    time.sleep(wait_time)
                else:
                    print(f"❌ API调用最终失败，已达到最大重试次数")

                    # 记录最终失败的详细信息
                    final_timestamp = datetime.now().isoformat()
                    print(f"📝 [线程{thread_id}] [{final_timestamp}] 错误处理总结:")
                    print(f"   - Token超限处理: {'是' if is_token_error else '否'}")
                    print(f"   - 速率限制重试: {rate_limit_retries}/{max_rate_limit_retries}")
                    print(f"   - 最终错误: {error_str[:200]}{'...' if len(error_str) > 200 else ''}")

        return None
    
    def _execute_api_call(self, api_params: dict, client) -> Optional[str]:
        """执行实际的API调用"""
        try:
            # 添加流式输出参数
            api_params["stream"] = True
            
            response = client.chat.completions.create(**api_params)
            
            # 处理流式响应
            full_response = ""
            print("📝 AI响应处理中...", end="", flush=True)
            
            response_start_time = time.time()
            total_timeout = 600  # 10分钟总超时
            last_content_time = time.time()
            content_timeout = 60  # 60秒无新内容超时
            
            thinking_dots = 0
            last_thinking_update = time.time()
            thinking_interval = 10
            thinking_warned = False
            content_started = False
            
            chunk_count = 0
            for chunk in response:
                chunk_count += 1
                current_time = time.time()
                elapsed = int(current_time - response_start_time)
                
                # 检查总响应时间是否超时
                if current_time - response_start_time > total_timeout:
                    print(f"\n⏰ 总响应超时 ({total_timeout//60}分钟)")
                    raise TimeoutError(f"总响应超时({total_timeout//60}分钟)")
                
                # 检查内容接收超时
                if full_response and current_time - last_content_time > content_timeout:
                    print(f"\n⏰ 内容传输中断 ({content_timeout}秒无新内容)")
                    raise TimeoutError(f"内容传输超时({content_timeout}秒)")
                
                # 显示思考进度
                if not content_started:
                    if elapsed > 180 and not thinking_warned:
                        print(f"\n💭 AI正在深度思考中，请耐心等待...")
                        thinking_warned = True
                    
                    if current_time - last_thinking_update > thinking_interval:
                        thinking_dots = (thinking_dots + 1) % 4
                        dots = "." * thinking_dots + " " * (3 - thinking_dots)
                        
                        if elapsed < 60:
                            print(f"\r📝 AI思考中{dots} ({elapsed}s)", end="", flush=True)
                        else:
                            minutes = elapsed // 60
                            seconds = elapsed % 60
                            print(f"\r📝 AI深度思考中{dots} ({minutes}m{seconds}s)", end="", flush=True)
                        
                        last_thinking_update = current_time
                
                # 处理chunk数据
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    if hasattr(choice, 'delta') and choice.delta:
                        delta_content = getattr(choice.delta, 'content', None)
                        if delta_content:
                            if not content_started:
                                elapsed_final = int(current_time - response_start_time)
                                if elapsed_final < 60:
                                    print(f"\r✨ AI思考完成({elapsed_final}s)，正在接收响应...")
                                else:
                                    minutes = elapsed_final // 60
                                    seconds = elapsed_final % 60
                                    print(f"\r✨ AI思考完成({minutes}m{seconds}s)，正在接收响应...")
                                content_started = True
                            
                            full_response += delta_content
                            last_content_time = current_time
                    
                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        if choice.finish_reason == 'stop':
                            break
                        elif choice.finish_reason == 'length':
                            print(f"\n⚠️ 响应因长度限制而截断")
                            break
                        elif choice.finish_reason == 'content_filter':
                            print(f"\n⚠️ 响应被内容过滤器拦截")
                            break
                        else:
                            print(f"\n⚠️ 响应异常结束: {choice.finish_reason}")
                            break
            
            # 响应完成处理
            if full_response:
                total_elapsed = int(time.time() - response_start_time)
                if total_elapsed < 60:
                    print(f"\r✅ 响应完成({total_elapsed}s)，内容长度: {len(full_response)} 字符")
                else:
                    minutes = total_elapsed // 60
                    seconds = total_elapsed % 60
                    print(f"\r✅ 响应完成({minutes}m{seconds}s)，内容长度: {len(full_response)} 字符")
                return full_response
            else:
                # 尝试非流式调用作为备用
                if chunk_count > 0:
                    print(f"\n🔄 尝试非流式调用作为备用方案...")
                    api_params_backup = api_params.copy()
                    api_params_backup["stream"] = False
                    
                    backup_response = client.chat.completions.create(**api_params_backup)
                    
                    if backup_response.choices and len(backup_response.choices) > 0:
                        backup_content = backup_response.choices[0].message.content
                        if backup_content:
                            print(f"✅ 非流式备用调用成功，内容长度: {len(backup_content)} 字符")
                            return backup_content
                
                return None
                
        except Exception as e:
            # 不在这里打印错误信息，让上层的call_api方法统一处理
            # 这样避免重复的错误日志
            raise e
    
    def update_config(self, step: str, params: Dict[str, Any]):
        """更新配置"""
        self.config["models"][step] = params
        self._save_config(self.config)
        print(f"✅ 已更新配置: {step}")
    
    def list_configs(self):
        """列出所有配置"""
        print("📋 当前模型配置:")
        for step, step_config in self.config["models"].items():
            model = step_config.get("model", "未设置")
            max_tokens = step_config.get("max_tokens", "未设置")
            reasoning = step_config.get("reasoning_effort", "N/A")
            print(f"  {step}: {model} (max_tokens: {max_tokens}, reasoning_effort: {reasoning})")


# 全局配置实例
model_config = ModelConfig()


def get_model_config() -> ModelConfig:
    """获取全局模型配置实例"""
    return model_config
