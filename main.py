# -*- coding: utf-8 -*-
"""
游戏舆情分析系统主入口 - 简化版
完整流程：SQL数据获取 → AI数据分析 → HTML报告生成
使用简化的AI API配置，专注于核心功能
"""

import json
import time
import os
import sys
from datetime import datetime, timedelta
# from concurrent.futures import ThreadPoolExecutor, as_completed  # 改为单线程处理，暂时注释
from pathlib import Path

# 导入现有模块
try:
    from TA import Ta
    from ai_analysis import (
        load_prompt_template,
        get_yesterday_date,
        get_sql_template,
        call_gemini_api as analyze_with_ai,
        analyze_comments_for_level
    )
    from html_generator import (
        load_prompt2_template,
        load_ai_analysis_json,
        call_gemini_api as generate_html_with_ai,
        clean_html_response,
        post_process_html,
        extract_data_summary
    )
    from html_to_image import convert_html_to_image
    from wecom_bot import SentimentReportBot
    from model_config import get_model_config

    print("✅ 所有模块导入成功")

except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("💡 请确保所有依赖文件都存在且配置正确")
    sys.exit(1)


def check_system_requirements():
    """检查系统要求和配置"""
    print("🔍 检查系统配置...")

    issues = []

    # 检查配置文件
    if not os.path.exists("model_config.json"):
        issues.append("❌ model_config.json 配置文件不存在")
    else:
        print("✅ model_config.json 配置文件存在")

    # 检查prompt文件
    if not os.path.exists("prompt1.md"):
        issues.append("❌ prompt1.md 文件不存在")
    else:
        print("✅ prompt1.md 文件存在")

    if not os.path.exists("prompt2.md"):
        issues.append("❌ prompt2.md 文件不存在")
    else:
        print("✅ prompt2.md 文件存在")

    # 检查AI API配置
    try:
        config = get_model_config()
        config.list_configs()
        print("✅ AI API 配置正常")
    except Exception as e:
        issues.append(f"❌ AI API 配置异常: {e}")

    # 创建data目录
    os.makedirs('data', exist_ok=True)
    print("✅ data 目录已准备")

    if issues:
        print("\n⚠️ 发现以下问题:")
        for issue in issues:
            print(f"  {issue}")
        print("\n💡 请解决上述问题后重新运行")
        return False

    print("✅ 系统检查通过")
    return True


class GameSentimentAnalyzer:
    """游戏舆情分析系统"""
    
    def __init__(self):
        self.analysis_date = get_yesterday_date()
        self.analysis_results = {}
        self.html_content = None
        self.html_file_path = None
        self.image_file_path = None
        
    def step1_data_collection_and_analysis(self):
        """步骤1: 数据收集和AI分析"""
        print("🔸 步骤1: 数据收集和AI分析")
        print("=" * 50)
        
        # 检查prompt1模板
        prompt_template = load_prompt_template()
        if not prompt_template:
            print("❌ 错误: 无法加载prompt1.md模板")
            return False
            
        print(f"📅 分析日期: {self.analysis_date}")
        print("🚀 开始多线程AI分析...")
        
        # 定义不同付费层级的查询条件 - 使用与原脚本一致的条件
        pay_levels_e = [
            ("小R用户", "AND uc.tag_value = '小R'"),
            ("中R用户", "AND uc.tag_value = '中R'"),
            ("大R用户", "AND uc.tag_value = '大R'"),
            ("超R用户", "AND uc.tag_value = '超R'"),
            ("零氪用户", "AND uc.tag_value IS NULL")
        ]
        
        pay_levels_d = [
            ("零氪用户", "AND uc.tag_value = '非R'"),
            ("小R用户", "AND uc.tag_value = '小R'"),
            ("中R用户", "AND uc.tag_value = '中R'"),
            ("大R用户", "AND uc.tag_value = '大R'"),
            ("超R用户", "AND uc.tag_value = '超R'")
        ]
        
        # 定义游戏版本
        game_versions = ['D', 'E']
        
        # 准备所有任务
        all_tasks = []
        for game_version in game_versions:
            pay_levels = pay_levels_e if game_version == 'E' else pay_levels_d
            for level_name, condition in pay_levels:
                all_tasks.append((level_name, condition, game_version))
        
        total_tasks = len(all_tasks)
        print(f"📋 准备执行 {total_tasks} 个分析任务，使用单线程顺序处理")
        
        # 使用单线程顺序处理
        start_time = time.time()
        completed_tasks = 0
        
        # 逐个处理任务
        for level_name, condition, game_version in all_tasks:
            completed_tasks += 1
            level_key = f"{game_version}版-{level_name}"
            
            print(f"\n🔄 开始处理任务 ({completed_tasks}/{total_tasks}): {level_key}")
            
            try:
                level_key, analysis_result = analyze_comments_for_level(
                    level_name, condition, self.analysis_date, game_version
                )
                self.analysis_results[level_key] = analysis_result
                
                # 显示任务完成状态
                if analysis_result and "ai_response" in analysis_result:
                    print(f"✅ 任务完成 ({completed_tasks}/{total_tasks}): {level_key}")
                else:
                    error_msg = analysis_result.get("error", "分析失败") if analysis_result else "分析失败"
                    print(f"❌ 任务失败 ({completed_tasks}/{total_tasks}): {level_key} - {error_msg}")
                
                # 显示总体进度
                progress = completed_tasks / total_tasks * 100
                print(f"📊 整体进度: {completed_tasks}/{total_tasks} ({progress:.1f}%)")
                
            except Exception as e:
                print(f"❌ 任务异常 ({completed_tasks}/{total_tasks}): {level_key} - {e}")
                # 添加错误结果
                self.analysis_results[level_key] = {"error": str(e)}
        
        print(f"\n🎯 所有任务完成，共处理 {total_tasks} 个任务")
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        # 保存AI分析结果（覆盖原文件）
        analysis_file = f"data/ai_analysis_{self.analysis_date}.json"
        final_results = {}
        
        success_count = 0
        total_users = 0
        total_messages = 0
        
        for level_key in self.analysis_results:
            result_data = self.analysis_results[level_key]
            if result_data and "ai_response" in result_data:
                final_results[level_key] = {
                    "ai_response": result_data["ai_response"],
                    "metadata": {
                        "total_comments": result_data.get("total_comments", 0),
                        "analyzed_comments": result_data.get("analyzed_comments", 0),
                        "unique_users": result_data.get("unique_users", 0)
                    }
                }
                success_count += 1
                total_users += result_data.get("unique_users", 0)
                total_messages += result_data.get("total_comments", 0)
                print(f"✅ {level_key}: 分析完成")
            else:
                error_msg = result_data.get("error", "分析失败") if result_data else "分析失败"
                final_results[level_key] = result_data or {"error": "分析失败"}
                # 针对SQL查询重试失败给出更详细的提示
                if "查询结果为空" in error_msg and "已重试" in error_msg:
                    print(f"❌ {level_key}: {error_msg}")
                else:
                    print(f"❌ {level_key}: 分析失败")
        
        # 写入分析结果文件（覆盖模式）
        print(f"💾 正在保存分析结果至: {analysis_file}")
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        end_time = time.time()
        
        print(f"\n📊 步骤1完成统计:")
        print(f"  ✅ 成功分析: {success_count}/10 个用户群体")
        print(f"  👥 总用户数: {total_users:,}")
        print(f"  💬 总消息数: {total_messages:,}")
        print(f"  📁 结果文件: {analysis_file}")
        print(f"  ⏱️  分析耗时: {end_time - start_time:.1f} 秒")
        print(f"  🔄 处理方式: 单线程顺序处理")
        
        return success_count > 0
    

    
    def step2_html_generation(self):
        """步骤2: HTML报告生成"""
        print("\n🔸 步骤2: HTML报告生成")
        print("=" * 50)
        
        # 加载prompt2模板
        prompt2_template = load_prompt2_template()
        if not prompt2_template:
            print("❌ 错误: 无法加载prompt2.md模板")
            return False
        
        # 加载AI分析结果
        analysis_file = f"data/ai_analysis_{self.analysis_date}.json"
        if not os.path.exists(analysis_file):
            print(f"❌ 错误: 找不到分析结果文件 {analysis_file}")
            return False
            
        analysis_data = load_ai_analysis_json(analysis_file)
        if not analysis_data:
            print("❌ 错误: 无法加载分析数据")
            return False
        
        # 提取数据摘要
        data_summary = extract_data_summary(analysis_data)
        print(f"📈 数据概况:")
        print(f"  👥 总用户数: {data_summary['total_users']:,}")
        print(f"  💬 总消息数: {data_summary['total_messages']:,}")
        print(f"  🎮 游戏版本: {list(data_summary['versions'].keys())}")
        print(f"  📊 用户层级: {sorted(data_summary['user_levels'])}")
        
        if data_summary["has_errors"]:
            print("  ⚠️  注意: 部分数据分析出现错误")
        
        # 计算统计数据（已去重）
        total_messages_dedup = data_summary['total_messages']
        total_users_dedup = data_summary['total_users']
        
        # 格式化日期
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(days=1)
        formatted_date = yesterday.strftime('%Y年%m月%d日')
        
        # 构建AI请求
        analysis_json_str = json.dumps(analysis_data, ensure_ascii=False, indent=2)
        
        messages = [
            {
                "role": "system",
                "content": prompt2_template
            },
            {
                "role": "user",
                "content": f"""根据以下JSON格式的游戏舆情分析数据，生成完整的HTML可视化日报。

请严格按照prompt2.md中的规则处理数据，生成包含完整HTML结构、CSS样式和数据内容的网页代码。

⚠️ 重要：请务必使用以下准确的统计数据：
- 分析日期：{formatted_date}
- 总消息数：{total_messages_dedup:,}条
- 总用户数：{total_users_dedup:,}人

🔥 关键要求：在HTML中显示日期时，必须使用：{formatted_date}
不要使用任何其他日期，不要硬编码日期！

在生成HTML时，请确保：
1. 所有日期显示都使用：{formatted_date}
2. 总消息数显示为：{total_messages_dedup:,}
3. 总用户数显示为：{total_users_dedup:,}
4. 不要在HTML中硬编码任何日期，必须使用上述提供的日期

分析数据:
```json
{analysis_json_str}
```

请生成完整的HTML代码，包括:
1. HTML文档结构 (<!DOCTYPE html>, <html>, <head>, <body>)
2. 内嵌CSS样式
3. 根据数据填充的所有占位符内容
4. 响应式设计，适配移动端和桌面端

再次强调：日期必须显示为 {formatted_date}，不要使用其他任何日期！"""
            }
        ]
        
        # 调用AI生成HTML
        print("🤖 正在调用AI生成HTML...")
        start_time = time.time()
        
        self.html_content = generate_html_with_ai(messages)
        
        if self.html_content:
            # 确保data目录存在
            os.makedirs('data', exist_ok=True)
            
            # 保存HTML文件（覆盖原文件）
            html_file = f"data/game_sentiment_report_{self.analysis_date}.html"
            
            try:
                print(f"💾 正在保存HTML文件至: {html_file}")
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(self.html_content)
                
                self.html_file_path = html_file
                
                end_time = time.time()
                
                print(f"\n📊 步骤2完成统计:")
                print(f"  ✅ HTML生成成功")
                print(f"  📁 HTML文件: {html_file}")
                print(f"  📄 文件大小: {len(self.html_content):,} 字符")
                print(f"  ⏱️  生成耗时: {end_time - start_time:.1f} 秒")
                
                return True
                
            except Exception as e:
                print(f"❌ 保存HTML文件失败: {e}")
                return False
        else:
            print("❌ HTML生成失败")
            return False
            
    def step3_image_generation(self):
        """步骤3: 将HTML转换为图片"""
        print("\n🔸 步骤3: HTML转图片")
        print("=" * 50)
        
        if not self.html_file_path or not os.path.exists(self.html_file_path):
            print("❌ 错误: HTML文件不存在，无法生成图片")
            return False
        
        try:
            # 确保data目录存在
            os.makedirs('data', exist_ok=True)
            
            # 生成图片文件名
            base_name = Path(self.html_file_path).stem
            image_file = f"data/{base_name}_screenshot.png"
            
            print(f"🖼️  开始HTML转图片...")
            
            # 转换HTML为图片
            success, image_path, file_size_mb = convert_html_to_image(
                self.html_file_path, 
                image_file
            )
            
            if success:
                self.image_file_path = image_path
                print(f"\n📊 步骤3完成统计:")
                print(f"  ✅ 图片生成成功")
                print(f"  🖼️  图片文件: {image_path}")
                print(f"  📊 文件大小: {file_size_mb:.2f} MB")
                return True
            else:
                print("❌ 图片生成失败")
                return False
                
        except Exception as e:
            print(f"❌ 图片生成异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
    
    def step4_wecom_push(self):
        """步骤4: 企业微信推送"""
        print("\n🔸 步骤4: 企业微信推送")
        print("=" * 50)
        
        try:
            # 直接创建推送机器人（使用硬编码的webhook配置）
            print("🤖 初始化企业微信机器人...")
            bot = SentimentReportBot()
            
            # 检查机器人是否正确初始化
            if not bot.webhook_url:
                print("❌ 企业微信机器人初始化失败：webhook地址为空")
                return False
            
            print(f"📡 使用Webhook: {bot.webhook_url}")
            
            # 获取分析摘要
            analysis_file = f"data/ai_analysis_{self.analysis_date}.json"
            analysis_data = load_ai_analysis_json(analysis_file)
            analysis_summary = None
            
            if analysis_data:
                analysis_summary = extract_data_summary(analysis_data)
            
            # 推送报告
            success = bot.send_daily_report(
                html_file=self.html_file_path,
                image_file=self.image_file_path,
                analysis_summary=analysis_summary
            )
            
            if success:
                print(f"\n📊 步骤4完成统计:")
                print(f"  ✅ 企业微信推送成功")
                return True
            else:
                print("❌ 企业微信推送失败")
                return False
                
        except Exception as e:
            print(f"❌ 企业微信推送异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
    
    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("🚀 游戏舆情分析系统启动")
        print("🎯 完整流程：SQL数据获取 → AI数据分析 → HTML报告生成 → 图片生成 → 企业微信推送")
        print("=" * 80)
        
        # 清理旧文件，确保全新开始
        self.cleanup_old_files()
        
        overall_start_time = time.time()
        
        # 步骤1：数据收集和AI分析
        if not self.step1_data_collection_and_analysis():
            print("❌ 步骤1失败，流程终止")
            return False
        
        # 步骤2：HTML报告生成
        if not self.step2_html_generation():
            print("❌ 步骤2失败，流程终止")
            return False
        
        # 步骤3：HTML转图片
        if not self.step3_image_generation():
            print("⚠️  步骤3失败，但流程继续（将使用HTML文件推送）")
        
        # 步骤4：企业微信推送
        if not self.step4_wecom_push():
            print("⚠️  步骤4失败，但分析流程已完成")
        
        # 完成总结
        overall_end_time = time.time()
        
        print(f"\n{'🎉 分析流程全部完成！' :=^80}")
        print(f"📅 分析日期: {self.analysis_date}")
        print(f"📁 输出文件:")
        print(f"  📊 AI分析结果: data/ai_analysis_{self.analysis_date}.json")
        print(f"  📈 HTML报告: {self.html_file_path or f'data/game_sentiment_report_{self.analysis_date}.html'}")
        if self.image_file_path:
            print(f"  🖼️  报告图片: {self.image_file_path}")
        print(f"⏱️  总耗时: {overall_end_time - overall_start_time:.1f} 秒")
        print("=" * 80)
        
        return True
    
    def cleanup_old_files(self):
        """清理当前日期的旧文件"""
        print("🧹 清理当前日期的旧文件...")
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        files_to_clean = [
            f"data/ai_analysis_{self.analysis_date}.json",
            f"data/game_sentiment_report_{self.analysis_date}.html",
            f"data/game_sentiment_report_{self.analysis_date}_screenshot.png"
        ]
        
        # 清理原始数据文件（所有版本和层级）
        import glob
        raw_data_pattern = f"data/raw_data_*_{self.analysis_date}.json"
        raw_data_files = glob.glob(raw_data_pattern)
        files_to_clean.extend(raw_data_files)
        
        cleaned_count = 0
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"  🗑️  删除: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  ⚠️  删除失败: {file_path} - {e}")
        
        if cleaned_count > 0:
            print(f"✅ 已清理 {cleaned_count} 个旧文件")
        else:
            print("✅ 无需清理旧文件")
        print()

def main():
    """主函数入口"""
    print("🚀 游戏舆情分析系统启动")
    print("=" * 60)

    # 系统检查
    if not check_system_requirements():
        return 1

    print("\n" + "=" * 60)

    # 解析命令行参数
    demo_mode = "--demo" in sys.argv
    help_mode = "--help" in sys.argv or "-h" in sys.argv

    if help_mode:
        print_help()
        return 0

    try:
        analyzer = GameSentimentAnalyzer()
        
        if demo_mode:
            print("🎯 演示模式启动")
            print("📝 使用模拟数据进行HTML生成演示...")
            
            # 创建模拟的分析数据用于演示
            demo_data = create_demo_analysis_data()
            
            # 确保data目录存在
            os.makedirs('data', exist_ok=True)
            
            # 保存演示数据
            demo_file = f"data/ai_analysis_{analyzer.analysis_date}_demo.json"
            with open(demo_file, 'w', encoding='utf-8') as f:
                json.dump(demo_data, f, ensure_ascii=False, indent=2)
            
            print(f"📁 演示数据已保存至: {demo_file}")
            
            # 临时修改分析日期文件名用于演示
            original_analysis_date = analyzer.analysis_date
            analyzer.analysis_date = f"{analyzer.analysis_date}_demo"
            
            # 只执行HTML生成步骤
            success = analyzer.step2_html_generation()
            
            # 恢复原始文件名
            analyzer.analysis_date = original_analysis_date
            
            if success:
                print("✅ 演示模式运行成功")
                print(f"📁 生成的HTML文件: data/game_sentiment_report_{analyzer.analysis_date}_demo.html")
                print("💡 提示: 可以用浏览器打开HTML文件查看效果")
                return 0
            else:
                print("❌ 演示模式运行失败")
                return 1
        else:
            # 正常模式：完整流程
            success = analyzer.run_complete_analysis()
            
            if success:
                print("✅ 系统运行成功")
                return 0
            else:
                print("❌ 系统运行失败")
                return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        return 1
    except Exception as e:
        print(f"\n💥 系统运行异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return 1

def print_help():
    """打印帮助信息"""
    print("""
🚀 游戏舆情分析系统 - 主入口程序
=========================================

📖 使用说明:
  python3 main.py          # 执行完整流程（SQL → AI分析 → HTML生成 → 图片生成 → 企业微信推送）
  python3 main.py --demo   # 演示模式（使用模拟数据生成HTML）
  python3 main.py --help   # 显示此帮助信息

🔧 完整流程:
  步骤0: 清理当前日期的旧文件（自动覆盖）
  步骤1: 从数据库获取用户言论数据
  步骤2: 使用AI分析数据（基于prompt1.md）+ 动态token管理
  步骤3: 智能响应式缩减（仅在超限时触发）
  步骤4: 生成结构化JSON分析结果
  步骤5: 使用AI生成HTML可视化报告（基于prompt2.md）
  步骤6: 将HTML报告转换为2MB以内的图片
  步骤7: 通过企业微信机器人推送报告

📁 输出文件（每次运行都会覆盖）:
  • ai_analysis_YYYY-MM-DD.json                    # AI分析结果
  • game_sentiment_report_YYYY-MM-DD.html          # HTML可视化报告
  • game_sentiment_report_YYYY-MM-DD_screenshot.png # 报告图片（≤2MB）
  • raw_data_版本_层级_YYYY-MM-DD.json              # 原始查询数据

🎯 演示模式:
  使用预设的模拟数据快速生成HTML报告，用于测试和演示系统功能。

🔧 环境配置:
  # 安装依赖
  pip install -r requirements.txt
  playwright install chromium
  
  # 企业微信推送配置（可选）
  export WECOM_BOT_WEBHOOK='你的企业微信机器人webhook地址'

🧪 功能测试:
  python3 test_image_and_push.py    # 测试HTML转图片和企业微信推送功能
  适用于：系统测试、功能演示、开发调试

🚀 智能Token管理特性:
  • 双重缩减策略：优先API精确计算，失败时tiktoken估算
  • 动态响应缩减机制：仅在API返回超限错误时自动缩减
  • 尽可能使用完整数据进行分析，最大化分析质量
  • 自动从API错误中提取精确token信息
  • 智能比例缩减：基于实际token使用量计算
  • 可靠fallback机制：tiktoken估算兜底保护
  • 完全避免API调用失败

⚠️  注意事项:
  • 每次运行都会重新执行完整流程
  • 同日期的旧文件会被自动清理和覆盖
  • 确保prompt1.md和prompt2.md文件存在
  • 确保数据库连接正常（正常模式）
  • 确保Gemini API配置正确
  • 大数据量分析可能需要较长时间（但不会因token超限失败）

🔗 相关文件:
  • ai_analysis.py    # AI数据分析模块
  • html_generator.py # HTML生成模块
  • TA.py            # 数据库连接模块
  • prompt1.md       # AI数据分析提示词
  • prompt2.md       # HTML生成提示词

💡 快速开始:
  1. 首次使用建议先运行演示模式了解系统功能
  2. 确认演示效果后再执行完整流程
  3. 每次运行都会生成全新的分析报告
  4. 可以在浏览器中打开生成的HTML文件查看报告
=========================================
""")

def create_demo_analysis_data():
    """创建演示用的分析数据"""
    demo_data = {}
    
    # 定义演示数据模板
    demo_template = {
        "ai_response": """{
    "用户群体概况": {
        "总用户数": 1500,
        "活跃用户数": 1200,
        "发言用户数": 800,
        "平均发言次数": 3.2
    },
    "情绪分析": {
        "正面情绪": 35,
        "中性情绪": 40,
        "负面情绪": 25,
        "整体情绪倾向": "轻微负面",
        "情绪健康度": 65
    },
    "热点话题": [
        {"话题": "游戏平衡性", "热度": 85, "情绪": "负面"},
        {"话题": "新版本内容", "热度": 70, "情绪": "中性"},
        {"话题": "充值活动", "热度": 60, "情绪": "正面"}
    ],
    "风险预警": [
        {
            "风险类型": "游戏体验",
            "风险等级": "中等",
            "风险描述": "部分玩家对新机制不满",
            "影响范围": "中R用户群体",
            "建议措施": "优化游戏机制，增加玩家引导"
        }
    ],
    "关键洞察": [
        "用户对新版本反馈以负面为主",
        "付费意愿有所下降",
        "需要关注用户留存情况"
    ]
}""",
        "metadata": {
            "total_comments": 2500,
            "analyzed_comments": 2500,
            "unique_users": 1500
        }
    }
    
    # 为每个用户群体创建数据
    user_levels = ["小R用户", "中R用户", "大R用户", "超R用户", "零氪用户"]
    game_versions = ["E版", "D版"]
    
    for version in game_versions:
        for level in user_levels:
            # 为不同群体调整数据
            level_data = demo_template.copy()
            level_data["metadata"] = demo_template["metadata"].copy()
            
            # 根据付费层级调整数据
            if "超R" in level:
                level_data["metadata"]["unique_users"] = 200
                level_data["metadata"]["total_comments"] = 800
            elif "大R" in level:
                level_data["metadata"]["unique_users"] = 500
                level_data["metadata"]["total_comments"] = 1500
            elif "中R" in level:
                level_data["metadata"]["unique_users"] = 800
                level_data["metadata"]["total_comments"] = 2000
            elif "小R" in level:
                level_data["metadata"]["unique_users"] = 1200
                level_data["metadata"]["total_comments"] = 2500
            else:  # 零氪
                level_data["metadata"]["unique_users"] = 3000
                level_data["metadata"]["total_comments"] = 5000
            
            demo_data[f"{version}-{level}"] = level_data
    
    return demo_data

if __name__ == "__main__":
    sys.exit(main()) 