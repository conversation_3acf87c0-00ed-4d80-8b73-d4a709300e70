# -*- coding: utf-8 -*-
"""
测试重复日志修复和Google Gemini API retryDelay提取
验证错误处理系统的日志清理和智能重试延迟提取
"""

import sys
import time
from model_config import IntelligentErrorHandler

def test_google_gemini_retry_delay_extraction():
    """测试Google Gemini API的retryDelay字段提取"""
    print("🧪 测试Google Gemini API retryDelay提取...")
    
    # 模拟真实的Google Gemini API 429错误响应
    gemini_error_examples = [
        # 实际的Google Gemini API错误格式
        """Error code: 429 - [{'error': {'code': 429, 'message': 'Resource has been exhausted (e.g. check quota).', 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'GenerateContentPaidTierInputTokensPerModelPerMinute', 'quota_limit_value': '1000000', 'quota_location': 'global', 'consumer': 'projects/123456789'}}], 'retryDelay': '31s'}}]""",
        
        # 另一种格式
        """Error code: 429 - [{'error': {'code': 429, 'message': 'You exceeded your current quota, please check your plan and billing details.', 'status': 'RESOURCE_EXHAUSTED', 'retryDelay': '45.5s'}}]""",
        
        # 没有retryDelay字段的情况
        """Error code: 429 - [{'error': {'code': 429, 'message': 'Too many requests', 'status': 'RESOURCE_EXHAUSTED'}}]""",
        
        # 传统格式
        "Rate limit exceeded. Please retry after 60 seconds.",
    ]
    
    handler = IntelligentErrorHandler()
    
    for i, error_msg in enumerate(gemini_error_examples, 1):
        print(f"\n📋 测试用例 {i}:")
        print(f"错误消息: {error_msg[:100]}...")
        
        is_rate_limit, suggested_delay = handler.parse_rate_limit_error(error_msg)
        
        print(f"结果: 是否速率限制错误={is_rate_limit}, 建议延迟={suggested_delay}秒")
        
        if is_rate_limit and suggested_delay:
            print(f"✅ 成功提取延迟时间: {suggested_delay}秒")
        elif is_rate_limit:
            print("⚠️ 检测到速率限制但未提取到延迟时间，将使用指数退避")
        else:
            print("❌ 未检测到速率限制错误")

def test_error_message_formats():
    """测试各种错误消息格式的识别"""
    print("\n🧪 测试错误消息格式识别...")
    
    error_formats = [
        # Google Gemini API格式
        ("Google Gemini", "Error code: 429 - [{'error': {'code': 429, 'retryDelay': '31s'}}]"),
        
        # OpenAI格式
        ("OpenAI", "Rate limit exceeded. Please retry after 60 seconds."),
        
        # 通用格式
        ("通用", "Too many requests. Wait 30 seconds."),
        
        # Token超限格式
        ("Token超限", "Error code: 400 - [{'error': {'code': 400, 'message': 'The input token count (1697036) exceeds the maximum number of tokens allowed (1048576).', 'status': 'INVALID_ARGUMENT'}}]"),
        
        # 非错误格式
        ("正常", "Request completed successfully"),
    ]
    
    handler = IntelligentErrorHandler()
    
    for api_type, error_msg in error_formats:
        print(f"\n📋 {api_type} API格式:")
        print(f"消息: {error_msg[:80]}...")
        
        # 测试速率限制检测
        is_rate_limit, delay = handler.parse_rate_limit_error(error_msg)
        print(f"速率限制: {is_rate_limit}, 延迟: {delay}秒")
        
        # 测试Token超限检测
        is_token_error, current, max_tokens = handler.parse_token_limit_error(error_msg)
        print(f"Token超限: {is_token_error}, 当前: {current}, 最大: {max_tokens}")

def test_concurrent_logging_simulation():
    """模拟并发日志记录"""
    print("\n🧪 测试并发日志记录...")
    
    import threading
    import time
    
    def simulate_api_call(thread_name, delay):
        """模拟API调用和错误处理"""
        thread_id = threading.get_ident()
        print(f"🔄 [线程{thread_id}] {thread_name}: 开始API调用")
        
        time.sleep(delay)  # 模拟API调用时间
        
        # 模拟错误处理
        error_msg = f"Error code: 429 - Rate limit exceeded for {thread_name}"
        print(f"❌ [线程{thread_id}] {thread_name}: API调用失败 - {error_msg}")
        
        handler = IntelligentErrorHandler()
        is_rate_limit, suggested_delay = handler.parse_rate_limit_error(error_msg)
        
        if is_rate_limit:
            print(f"🎯 [线程{thread_id}] {thread_name}: 检测到速率限制错误")
            wait_time = suggested_delay or 2
            print(f"⏰ [线程{thread_id}] {thread_name}: 等待 {wait_time} 秒...")
            time.sleep(wait_time)
            print(f"✅ [线程{thread_id}] {thread_name}: 重试完成")
    
    # 创建多个线程模拟并发
    threads = []
    for i in range(3):
        thread = threading.Thread(
            target=simulate_api_call, 
            args=(f"任务{i+1}", 0.1 * i)
        )
        threads.append(thread)
    
    # 启动所有线程
    print("🚀 启动并发测试...")
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("✅ 并发测试完成")

def test_logging_improvements():
    """测试日志改进"""
    print("\n🧪 测试日志改进...")
    
    print("📋 改进前后对比:")
    print("❌ 改进前: 重复日志")
    print("   ❌ API调用异常: Error code: 429 - [...]")
    print("   ❌ API调用失败 (尝试 1/3): Error code: 429 - [...]")
    print()
    print("✅ 改进后: 清晰日志")
    print("   ❌ [线程12345] API调用失败 (尝试 1/3): Error code: 429 - [...]")
    print("   🎯 [线程12345] [2025-01-16T10:30:45] 检测到速率限制错误 (重试 1/5)")
    print("   ⏰ [线程12345] 使用API建议延迟: 31 秒 (原始: 31秒)")
    print("   💤 [线程12345] 开始等待 31 秒...")
    print("   🔄 [线程12345] 等待完成，重新尝试API调用...")
    print()
    print("🎯 改进要点:")
    print("  ✅ 消除重复日志: 移除_execute_api_call中的冗余错误日志")
    print("  ✅ 线程标识: 所有日志包含线程ID，便于调试并发问题")
    print("  ✅ 时间戳: 详细的错误处理时间记录")
    print("  ✅ 智能延迟: 优先使用API提供的retryDelay")
    print("  ✅ 清晰状态: 明确的等待和重试状态提示")

def main():
    """运行所有测试"""
    print("🚀 重复日志修复和智能重试延迟测试")
    print("=" * 80)
    
    try:
        # 运行各项测试
        test_google_gemini_retry_delay_extraction()
        test_error_message_formats()
        test_concurrent_logging_simulation()
        test_logging_improvements()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成！")
        
        print("\n🎯 修复总结:")
        print("  ✅ 重复日志问题: 已修复，移除_execute_api_call中的冗余日志")
        print("  ✅ Google Gemini retryDelay: 已支持，优先使用API建议延迟")
        print("  ✅ 线程安全日志: 所有日志包含线程ID标识")
        print("  ✅ 智能重试策略: API延迟 > 指数退避 > 默认延迟")
        print("  ✅ 错误消息截断: 长错误消息自动截断，避免日志污染")
        
        print("\n🚀 现在的错误处理流程:")
        print("  1. 捕获API异常 (无重复日志)")
        print("  2. 解析错误类型和参数")
        print("  3. 应用智能处理策略")
        print("  4. 记录详细的处理日志")
        print("  5. 执行重试或返回结果")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
