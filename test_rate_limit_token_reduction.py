# -*- coding: utf-8 -*-
"""
测试速率限制时的Token缩减功能
验证当遇到2次或更多速率限制错误时，系统会自动进行token缩减
"""

import sys
import time
from model_config import IntelligentErrorHandler

def test_rate_limit_token_reduction_logic():
    """测试速率限制时的token缩减逻辑"""
    print("🧪 测试速率限制Token缩减逻辑...")
    
    # 创建一个大型消息，模拟需要缩减的场景
    large_content = "请分析以下数据：" + "这是大量的测试数据内容，用于模拟需要缩减的情况。" * 5000
    
    messages = [
        {
            "role": "system",
            "content": "你是一个专业的数据分析师，请仔细分析用户提供的所有数据。"
        },
        {
            "role": "user",
            "content": large_content
        }
    ]
    
    handler = IntelligentErrorHandler()
    
    # 计算原始token数
    original_tokens = handler.estimate_messages_tokens(messages)
    print(f"📊 原始消息Token数: {original_tokens:,}")
    
    # 模拟速率限制缩减场景
    target_tokens = 900000  # 90万tokens
    
    if original_tokens > target_tokens:
        print(f"🎯 模拟速率限制缩减: {original_tokens:,} → {target_tokens:,}")
        
        # 测试缩减功能
        reduced_messages = handler.reduce_content_to_target_tokens(messages, target_tokens)
        reduced_tokens = handler.estimate_messages_tokens(reduced_messages)
        
        print(f"✅ 缩减结果: {original_tokens:,} → {reduced_tokens:,} tokens")
        print(f"📝 内容长度: {len(messages[1]['content'])} → {len(reduced_messages[1]['content'])} 字符")
        
        # 验证缩减是否达到目标
        if reduced_tokens <= target_tokens:
            print("✅ 速率限制缩减有效，达到90万token目标")
        else:
            print(f"⚠️ 缩减后仍超过目标: {reduced_tokens:,} > {target_tokens:,}")
        
        # 计算缩减比例
        reduction_ratio = reduced_tokens / original_tokens
        print(f"📊 缩减比例: {reduction_ratio:.1%}")
        
    else:
        print(f"ℹ️ 原始消息({original_tokens:,})已小于90万，无需缩减")

def test_rate_limit_scenarios():
    """测试不同的速率限制场景"""
    print("\n🧪 测试速率限制场景...")
    
    scenarios = [
        {
            "name": "第1次速率限制",
            "retry_count": 1,
            "should_reduce": False,
            "description": "仅等待重试，不进行token缩减"
        },
        {
            "name": "第2次速率限制",
            "retry_count": 2,
            "should_reduce": True,
            "description": "触发token缩减到90万"
        },
        {
            "name": "第3次速率限制",
            "retry_count": 3,
            "should_reduce": True,
            "description": "如果之前未缩减，则进行缩减"
        },
        {
            "name": "第4次速率限制",
            "retry_count": 4,
            "should_reduce": True,
            "description": "继续使用缩减后的内容重试"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 场景: {scenario['name']}")
        print(f"重试次数: {scenario['retry_count']}")
        print(f"是否缩减: {'是' if scenario['should_reduce'] else '否'}")
        print(f"说明: {scenario['description']}")
        
        # 模拟逻辑判断
        if scenario['retry_count'] >= 2:
            print("🔄 触发条件: rate_limit_retries >= 2")
            print("📊 执行: 缩减到90万tokens")
            print("💡 原理: 较小的请求更容易通过速率限制")
        else:
            print("⏰ 执行: 仅等待重试")

def test_rate_limit_error_parsing():
    """测试速率限制错误解析"""
    print("\n🧪 测试速率限制错误解析...")
    
    rate_limit_errors = [
        # Google Gemini API格式
        "Error code: 429 - [{'error': {'code': 429, 'message': 'Resource has been exhausted (e.g. check quota).', 'status': 'RESOURCE_EXHAUSTED', 'retryDelay': '31s'}}]",
        
        # 配额超限格式
        "Error code: 429 - [{'error': {'code': 429, 'message': 'You exceeded your current quota, please check your plan and billing details.', 'status': 'RESOURCE_EXHAUSTED'}}]",
        
        # 标准格式
        "Rate limit exceeded. Please retry after 60 seconds.",
        
        # 简化格式
        "Too many requests. Wait 30 seconds.",
    ]
    
    handler = IntelligentErrorHandler()
    
    for i, error_msg in enumerate(rate_limit_errors, 1):
        print(f"\n📋 错误格式 {i}:")
        print(f"消息: {error_msg[:80]}...")
        
        is_rate_limit, suggested_delay = handler.parse_rate_limit_error(error_msg)
        
        if is_rate_limit:
            print(f"✅ 检测到速率限制错误")
            if suggested_delay:
                print(f"⏰ 建议延迟: {suggested_delay}秒")
            else:
                print("⏰ 使用指数退避策略")
            print("🔄 第2次重试时将触发token缩减")
        else:
            print("❌ 未检测到速率限制错误")

def test_combined_error_handling():
    """测试组合错误处理策略"""
    print("\n🧪 测试组合错误处理策略...")
    
    print("📋 智能错误处理优先级:")
    print("1. Token超限错误 (400 - INVALID_ARGUMENT)")
    print("   - 立即缩减到最大值的90%")
    print("   - 立即重试，不计入重试次数")
    print()
    print("2. 速率限制错误 (429 - RESOURCE_EXHAUSTED)")
    print("   - 第1次: 等待重试 (使用API建议延迟或指数退避)")
    print("   - 第2次+: 缩减到90万tokens + 等待重试")
    print("   - 最多5次重试，最大等待300秒")
    print()
    print("3. 其他错误 (连接、服务器等)")
    print("   - 使用指数退避策略重试")
    print("   - 计入主重试次数")
    
    print("\n🎯 组合策略优势:")
    print("✅ Token超限: 精确缩减，立即恢复")
    print("✅ 速率限制: 先等待，再缩减，双重保障")
    print("✅ 网络错误: 智能重试，逐步增加延迟")
    print("✅ 线程安全: 所有日志包含线程ID")
    print("✅ 详细记录: 完整的错误处理过程日志")

def test_token_reduction_thresholds():
    """测试token缩减阈值"""
    print("\n🧪 测试Token缩减阈值...")
    
    test_cases = [
        {"tokens": 1500000, "should_reduce": True, "reason": "超过90万"},
        {"tokens": 1000000, "should_reduce": True, "reason": "超过90万"},
        {"tokens": 900000, "should_reduce": False, "reason": "等于90万"},
        {"tokens": 800000, "should_reduce": False, "reason": "小于90万"},
        {"tokens": 500000, "should_reduce": False, "reason": "远小于90万"},
    ]
    
    for case in test_cases:
        tokens = case["tokens"]
        should_reduce = case["should_reduce"]
        reason = case["reason"]
        
        print(f"\n📊 Token数: {tokens:,}")
        print(f"是否缩减: {'是' if should_reduce else '否'} ({reason})")
        
        if should_reduce:
            target = 900000
            reduction_ratio = target / tokens
            print(f"缩减目标: {target:,} tokens")
            print(f"缩减比例: {reduction_ratio:.1%}")
        else:
            print("无需缩减，直接重试")

def main():
    """运行所有测试"""
    print("🚀 速率限制Token缩减功能测试")
    print("=" * 80)
    
    try:
        # 运行各项测试
        test_rate_limit_token_reduction_logic()
        test_rate_limit_scenarios()
        test_rate_limit_error_parsing()
        test_combined_error_handling()
        test_token_reduction_thresholds()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成！")
        
        print("\n🎯 速率限制Token缩减特性:")
        print("  ✅ 第1次速率限制: 仅等待重试")
        print("  ✅ 第2次+速率限制: 缩减到90万tokens + 等待重试")
        print("  ✅ 智能判断: 只有当前token数 > 90万时才缩减")
        print("  ✅ 双重保障: 缩减 + 等待，提高成功率")
        print("  ✅ 详细日志: 记录缩减过程和效果")
        
        print("\n💡 设计理念:")
        print("  🔹 减少API负载: 较小的请求更容易通过速率限制")
        print("  🔹 渐进式处理: 先等待，再缩减，避免过度缩减")
        print("  🔹 智能阈值: 90万tokens平衡了内容完整性和成功率")
        print("  🔹 用户友好: 自动处理，无需手动干预")
        
        print("\n🚀 现在的错误处理流程:")
        print("  1. Token超限 → 立即缩减到90% → 重试")
        print("  2. 第1次速率限制 → 等待重试")
        print("  3. 第2次+速率限制 → 缩减到90万 + 等待重试")
        print("  4. 其他错误 → 指数退避重试")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
